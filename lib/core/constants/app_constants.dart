class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // App name
  static const String appName = 'Beta Transactions';

  // Database
  static const String databaseName = 'beta_transactions.db';
  static const int databaseVersion = 2;

  // Routes
  static const String itemsRoute = '/items';
  static const String newTransactionRoute = '/new-transaction';
  static const String unpaidTransactionsRoute = '/unpaid-transactions';
  static const String transactionHistoryRoute = '/transaction-history';

  // Navigation
  static const int itemsTabIndex = 0;
  static const int newTransactionTabIndex = 1;
  static const int unpaidTransactionsTabIndex = 2;
  static const int transactionHistoryTabIndex = 3;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double cardBorderRadius = 12.0;
  static const double bottomSheetBorderRadius = 16.0;

  // Validation
  static const int minItemNameLength = 2;
  static const int minQuantity = 1;
  static const int minPrice = 1;

  // Currency
  static const String currencySymbol = 'Rp';
  static const String currencyFormat = '#,###';
  // Removed decimal format as we're using whole numbers only

  // Date formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  // Transaction status
  static const String statusUnpaid = 'Unpaid';
  static const String statusPartiallyPaid = 'Partially Paid';
  static const String statusPaid = 'Paid_'; // Added underscore to meet minimum length requirement of 5 characters

  // Error messages
  static const String errorRequiredField = 'This field is required';
  static const String errorMinLength = 'Minimum length is $minItemNameLength characters';
  static const String errorMinQuantity = 'Minimum quantity is $minQuantity';
  static const String errorMinPrice = 'Price must be greater than zero';
  static const String errorInvalidNumber = 'Please enter a valid number';
  static const String errorDuplicateItem = 'An item with this name already exists';
  static const String errorExceedingPayment = 'Payment amount cannot exceed the remaining amount';
  static const String errorNoItems = 'Please add at least one item to the transaction';
  static const String errorInvalidCharacters = 'Name contains invalid characters';

  // Success messages
  static const String successItemAdded = 'Item added successfully';
  static const String successItemUpdated = 'Item updated successfully';
  static const String successItemDeleted = 'Item deleted successfully';
  static const String successTransactionCreated = 'Transaction created successfully';
  static const String successPaymentAdded = 'Payment added successfully';

  // Confirmation messages
  static const String confirmDeleteItem = 'Are you sure you want to delete this item?';
  static const String confirmUpdateItem = 'Are you sure you want to update this item?';
  static const String confirmCreateTransaction = 'Are you sure you want to create this transaction?';

  // Button labels
  static const String buttonAdd = 'Add';
  static const String buttonUpdate = 'Update';
  static const String buttonDelete = 'Delete';
  static const String buttonCancel = 'Cancel';
  static const String buttonConfirm = 'Confirm';
  static const String buttonPay = 'Pay';
  static const String buttonPayAll = 'Pay All';
  static const String buttonAddAnotherItem = 'Add Another Item';
  static const String buttonRemoveItem = 'Remove';
  static const String buttonCreateTransaction = 'Create Transaction';

  // Screen titles
  static const String titleItems = 'Items';
  static const String titleNewTransaction = 'New Transaction';
  static const String titleUnpaidTransactions = 'Unpaid Transactions';
  static const String titleTransactionHistory = 'Transaction History';
  static const String titleAddItem = 'Add Item';
  static const String titleEditItem = 'Edit Item';
  static const String titlePayment = 'Make Payment';
  static const String titleGlobalPayment = 'Make Global Payment';

  // Form labels
  static const String labelItemName = 'Item Name';
  static const String labelItemPrice = 'Price';
  static const String labelQuantity = 'Quantity';
  static const String labelDate = 'Date';
  static const String labelTotalAmount = 'Total Amount';
  static const String labelRemainingAmount = 'Remaining Amount';
  static const String labelPaymentAmount = 'Payment Amount';
  static const String labelGrandTotal = 'Grand Total';
  static const String labelSearch = 'Search';

  // Empty state messages
  static const String emptyItems = 'No items found';
  static const String emptyTransactions = 'No transactions found';
  static const String emptyUnpaidTransactions = 'No unpaid transactions found';
  static const String emptyTransactionHistory = 'No transaction history found';
  static const String emptySearchResults = 'No results found';
}
