import 'package:flutter/material.dart';

class AppTypography {
  // Private constructor to prevent instantiation
  AppTypography._();

  // Base font family
  static const String _fontFamily = 'Roboto';

  // Line height and letter spacing
  static const double _defaultLineHeight = 1.5;
  static const double _defaultLetterSpacing = 0.5;

  // Text styles for light theme
  static TextTheme get textTheme {
    return const TextTheme(
      // Headings
      displayLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      displayMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      displaySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      
      // Body text
      bodyLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      bodyMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      bodySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black54,
      ),
      
      // Labels
      labelLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      labelMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black87,
      ),
      labelSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.black54,
      ),
    );
  }

  // Text styles for dark theme
  static TextTheme get darkTextTheme {
    return const TextTheme(
      // Headings
      displayLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      displayMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      displaySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      
      // Body text
      bodyLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      bodyMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      bodySmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.normal,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white70,
      ),
      
      // Labels
      labelLarge: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      labelMedium: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white,
      ),
      labelSmall: TextStyle(
        fontFamily: _fontFamily,
        fontSize: 10,
        fontWeight: FontWeight.w500,
        letterSpacing: _defaultLetterSpacing,
        height: _defaultLineHeight,
        color: Colors.white70,
      ),
    );
  }
}
