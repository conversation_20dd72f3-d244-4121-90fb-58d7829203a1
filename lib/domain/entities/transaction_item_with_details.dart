import 'package:freezed_annotation/freezed_annotation.dart';
import 'item.dart';
import 'transaction_item.dart';

part 'transaction_item_with_details.freezed.dart';
part 'transaction_item_with_details.g.dart';

@freezed
class TransactionItemWithDetailsEntity with _$TransactionItemWithDetailsEntity {
  const factory TransactionItemWithDetailsEntity({
    required TransactionItemEntity transactionItem,
    required ItemEntity item,
  }) = _TransactionItemWithDetailsEntity;

  factory TransactionItemWithDetailsEntity.fromJson(Map<String, dynamic> json) => _$TransactionItemWithDetailsEntityFromJson(json);
}
