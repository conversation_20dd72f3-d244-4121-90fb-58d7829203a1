// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'complete_transaction_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CompleteTransactionDetailsEntityImpl
_$$CompleteTransactionDetailsEntityImplFromJson(Map<String, dynamic> json) =>
    _$CompleteTransactionDetailsEntityImpl(
      transaction: TransactionEntity.fromJson(
        json['transaction'] as Map<String, dynamic>,
      ),
      items:
          (json['items'] as List<dynamic>)
              .map(
                (e) => TransactionItemWithDetailsEntity.fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
      payments:
          (json['payments'] as List<dynamic>)
              .map((e) => PaymentEntity.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$$CompleteTransactionDetailsEntityImplToJson(
  _$CompleteTransactionDetailsEntityImpl instance,
) => <String, dynamic>{
  'transaction': instance.transaction,
  'items': instance.items,
  'payments': instance.payments,
};
