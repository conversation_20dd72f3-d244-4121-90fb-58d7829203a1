import 'package:freezed_annotation/freezed_annotation.dart';
import 'transaction.dart';
import 'transaction_item_with_details.dart';

part 'transaction_with_items.freezed.dart';
part 'transaction_with_items.g.dart';

@freezed
class TransactionWithItemsEntity with _$TransactionWithItemsEntity {
  const factory TransactionWithItemsEntity({
    required TransactionEntity transaction,
    required List<TransactionItemWithDetailsEntity> items,
  }) = _TransactionWithItemsEntity;

  factory TransactionWithItemsEntity.fromJson(Map<String, dynamic> json) => _$TransactionWithItemsEntityFromJson(json);
}
