// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'paid_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PaidItemEntityImpl _$$PaidItemEntityImplFromJson(Map<String, dynamic> json) =>
    _$PaidItemEntityImpl(
      id: (json['id'] as num).toInt(),
      paymentId: (json['paymentId'] as num).toInt(),
      transactionItemId: (json['transactionItemId'] as num).toInt(),
      quantity: (json['quantity'] as num).toInt(),
      amount: (json['amount'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$PaidItemEntityImplToJson(
  _$PaidItemEntityImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'paymentId': instance.paymentId,
  'transactionItemId': instance.transactionItemId,
  'quantity': instance.quantity,
  'amount': instance.amount,
  'createdAt': instance.createdAt.toIso8601String(),
};
