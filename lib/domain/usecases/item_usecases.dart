import '../entities/item.dart';
import '../repositories/item_repository.dart';

class GetAllItemsUseCase {
  final ItemRepository repository;

  GetAllItemsUseCase(this.repository);

  Future<List<ItemEntity>> call() {
    return repository.getAllItems();
  }
}

class SearchItemsUseCase {
  final ItemRepository repository;

  SearchItemsUseCase(this.repository);

  Future<List<ItemEntity>> call(String query) {
    return repository.searchItems(query);
  }
}

class GetItemByIdUseCase {
  final ItemRepository repository;

  GetItemByIdUseCase(this.repository);

  Future<ItemEntity> call(int id) {
    return repository.getItemById(id);
  }
}

class IsItemNameExistsUseCase {
  final ItemRepository repository;

  IsItemNameExistsUseCase(this.repository);

  Future<bool> call(String name) {
    return repository.isItemNameExists(name);
  }
}

class AddItemUseCase {
  final ItemRepository repository;

  AddItemUseCase(this.repository);

  Future<int> call(ItemEntity item) {
    return repository.addItem(item);
  }
}

class UpdateItemUseCase {
  final ItemRepository repository;

  UpdateItemUseCase(this.repository);

  Future<bool> call(ItemEntity item) {
    return repository.updateItem(item);
  }
}

class DeleteItemUseCase {
  final ItemRepository repository;

  DeleteItemUseCase(this.repository);

  Future<int> call(int id) {
    return repository.deleteItem(id);
  }
}
