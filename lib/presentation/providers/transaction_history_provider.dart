import 'dart:developer' as developer;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/transaction_with_payments.dart';
import '../../domain/usecases/transaction_usecases.dart';
import 'providers.dart';

// Transaction history notifier
class TransactionHistoryNotifier extends StateNotifier<AsyncValue<List<TransactionWithPaymentsEntity>>> {
  final GetPaidTransactionsUseCase _getPaidTransactionsUseCase;
  final GetTransactionWithPaymentsUseCase _getTransactionWithPaymentsUseCase;
  final GetCompleteTransactionDetailsUseCase _getCompleteTransactionDetailsUseCase;

  TransactionHistoryNotifier({
    required GetPaidTransactionsUseCase getPaidTransactionsUseCase,
    required GetTransactionWithPaymentsUseCase getTransactionWithPaymentsUseCase,
    required GetCompleteTransactionDetailsUseCase getCompleteTransactionDetailsUseCase,
    required GetPaymentPaidItemsUseCase getPaymentPaidItemsUseCase,
  })  : _getPaidTransactionsUseCase = getPaidTransactionsUseCase,
        _getTransactionWithPaymentsUseCase = getTransactionWithPaymentsUseCase,
        _getCompleteTransactionDetailsUseCase = getCompleteTransactionDetailsUseCase,
        super(const AsyncValue.loading()) {
    refreshTransactions();
  }

  Future<void> refreshTransactions() async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _getPaidTransactionsUseCase();

      // Get transaction with payments for each transaction
      final transactionsWithPayments = <TransactionWithPaymentsEntity>[];
      for (final transaction in transactions) {
        final transactionWithPayments = await _getTransactionWithPaymentsUseCase(transaction.id);
        transactionsWithPayments.add(transactionWithPayments);
      }

      state = AsyncValue.data(transactionsWithPayments);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }



  Future<List<TransactionWithPaymentsEntity>> searchTransactions(String query) async {
    if (state is! AsyncData) {
      return [];
    }

    final transactions = (state as AsyncData<List<TransactionWithPaymentsEntity>>).value;
    final results = <TransactionWithPaymentsEntity>[];

    for (final transaction in transactions) {
      // Get complete transaction details to search by item name
      final completeDetails = await _getCompleteTransactionDetailsUseCase(transaction.transaction.id);

      // Check if any item name contains the query
      final containsItem = completeDetails.items.any(
        (item) => item.item.name.toLowerCase().contains(query.toLowerCase()),
      );

      if (containsItem) {
        results.add(transaction);
      }
    }

    return results;
  }
}

// Base transaction history provider (without filtering)
final baseTransactionHistoryProvider = StateNotifierProvider<TransactionHistoryNotifier, AsyncValue<List<TransactionWithPaymentsEntity>>>((ref) {
  return TransactionHistoryNotifier(
    getPaidTransactionsUseCase: ref.watch(getPaidTransactionsUseCaseProvider),
    getTransactionWithPaymentsUseCase: ref.watch(getTransactionWithPaymentsUseCaseProvider),
    getCompleteTransactionDetailsUseCase: ref.watch(getCompleteTransactionDetailsUseCaseProvider),
    getPaymentPaidItemsUseCase: ref.watch(getPaymentPaidItemsUseCaseProvider),
  );
});

// Transaction history provider (no filtering)
final transactionHistoryProvider = Provider<AsyncValue<List<TransactionWithPaymentsEntity>>>((ref) {
  return ref.watch(baseTransactionHistoryProvider);
});

// Search transaction history provider
final searchTransactionHistoryProvider = FutureProvider.family<List<TransactionWithPaymentsEntity>, String>((ref, query) async {
  final notifier = ref.watch(baseTransactionHistoryProvider.notifier);
  return notifier.searchTransactions(query);
});

// Provider for transactions grouped by payment date
final paymentDateGroupedTransactionsProvider = Provider<AsyncValue<Map<DateTime, List<TransactionWithPaymentsEntity>>>>((ref) {
  final transactionsState = ref.watch(transactionHistoryProvider);

  return transactionsState.when(
    data: (transactions) {
      // Group transactions by payment date
      final groupedByPaymentDate = <DateTime, List<TransactionWithPaymentsEntity>>{};

      for (final transaction in transactions) {
        // For each transaction, get all its payments
        for (final payment in transaction.payments) {
          // Use payment date as the key (normalized to day)
          final paymentDateKey = DateTime(payment.date.year, payment.date.month, payment.date.day);

          if (!groupedByPaymentDate.containsKey(paymentDateKey)) {
            groupedByPaymentDate[paymentDateKey] = [];
          }

          // Check if this transaction is already added for this payment date
          final alreadyAdded = groupedByPaymentDate[paymentDateKey]!.any(
            (t) => t.transaction.id == transaction.transaction.id
          );

          if (!alreadyAdded) {
            groupedByPaymentDate[paymentDateKey]!.add(transaction);
          }
        }
      }

      return AsyncValue.data(groupedByPaymentDate);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// Class to hold grouped item data for display
class GroupedItemData {
  final String itemName;
  final int totalQuantity;
  final double totalAmount;

  GroupedItemData({
    required this.itemName,
    required this.totalQuantity,
    required this.totalAmount,
  });
}

// Provider for getting paid items grouped by item name for a specific payment date
final paidItemsByDateProvider = FutureProvider.family<List<GroupedItemData>, DateTime>((ref, date) async {
  final transactionsState = ref.watch(transactionHistoryProvider);

  if (transactionsState is! AsyncData) {
    return [];
  }

  final transactions = transactionsState.value ?? [];
  final groupedItems = <String, GroupedItemData>{};

  for (final transaction in transactions) {
    for (final payment in transaction.payments) {
      try {
        // Check if this payment was made on the specified date
        final paymentDate = DateTime(payment.date.year, payment.date.month, payment.date.day);
        final targetDate = DateTime(date.year, date.month, date.day);

        if (paymentDate.isAtSameMomentAs(targetDate)) {
          // Get the transaction details
          final transactionDetails = await ref.read(getCompleteTransactionDetailsUseCaseProvider).call(transaction.transaction.id);

          try {
            // Get the paid items for this payment
            final paidItems = await ref.read(getPaymentPaidItemsUseCaseProvider).call(payment.id);

            // If we have paid items records, use them
            if (paidItems.isNotEmpty) {
              // Group the paid items by item name
              for (final paidItem in paidItems) {
                try {
                  // Find the transaction item
                  final matchingItems = transactionDetails.items.where(
                    (item) => item.transactionItem.id == paidItem.transactionItemId,
                  ).toList();

                  if (matchingItems.isNotEmpty) {
                    final transactionItem = matchingItems.first;
                    final itemName = transactionItem.item.name;

                    if (!groupedItems.containsKey(itemName)) {
                      groupedItems[itemName] = GroupedItemData(
                        itemName: itemName,
                        totalQuantity: 0,
                        totalAmount: 0,
                      );
                    }

                    // Update the grouped item data
                    final currentData = groupedItems[itemName]!;
                    groupedItems[itemName] = GroupedItemData(
                      itemName: itemName,
                      totalQuantity: currentData.totalQuantity + paidItem.quantity,
                      totalAmount: currentData.totalAmount + paidItem.amount,
                    );
                  }
                } catch (e) {
                  // Skip this paid item if there's an error
                  developer.log('Error processing paid item: $e');
                  continue;
                }
              }
            } else {
              // Fallback: If no paid items records, use transaction items as fallback
              // This handles payments made before the paid items system was implemented
              if (transaction.transaction.status == AppConstants.statusPaid) {
                // Only use fully paid transactions for fallback
                for (final item in transactionDetails.items) {
                  final itemName = item.item.name;
                  final itemQuantity = item.transactionItem.quantity;
                  final itemAmount = item.transactionItem.quantity * item.transactionItem.priceAtPurchase;

                  if (!groupedItems.containsKey(itemName)) {
                    groupedItems[itemName] = GroupedItemData(
                      itemName: itemName,
                      totalQuantity: 0,
                      totalAmount: 0,
                    );
                  }

                  // Update the grouped item data
                  final currentData = groupedItems[itemName]!;
                  groupedItems[itemName] = GroupedItemData(
                    itemName: itemName,
                    totalQuantity: currentData.totalQuantity + itemQuantity,
                    totalAmount: currentData.totalAmount + itemAmount,
                  );
                }
              }
            }
          } catch (e) {
            // Fallback: If error getting paid items, use transaction items as fallback
            developer.log('Error getting paid items for payment ${payment.id}: $e');
            if (transaction.transaction.status == AppConstants.statusPaid) {
              // Only use fully paid transactions for fallback
              for (final item in transactionDetails.items) {
                final itemName = item.item.name;
                final itemQuantity = item.transactionItem.quantity;
                final itemAmount = item.transactionItem.quantity * item.transactionItem.priceAtPurchase;

                if (!groupedItems.containsKey(itemName)) {
                  groupedItems[itemName] = GroupedItemData(
                    itemName: itemName,
                    totalQuantity: 0,
                    totalAmount: 0,
                  );
                }

                // Update the grouped item data
                final currentData = groupedItems[itemName]!;
                groupedItems[itemName] = GroupedItemData(
                  itemName: itemName,
                  totalQuantity: currentData.totalQuantity + itemQuantity,
                  totalAmount: currentData.totalAmount + itemAmount,
                );
              }
            }
          }
        }
      } catch (e) {
        // Skip this payment if there's an error
        developer.log('Error processing payment: $e');
        continue;
      }
    }
  }

  // Convert the map to a list and sort by item name
  final result = groupedItems.values.toList()
    ..sort((a, b) => a.itemName.compareTo(b.itemName));

  return result;
});


