import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/item.dart';
import '../../domain/usecases/item_usecases.dart';
import 'providers.dart';

// Items state notifier
class ItemsNotifier extends StateNotifier<AsyncValue<List<ItemEntity>>> {
  final GetAllItemsUseCase _getAllItemsUseCase;
  // SearchItemsUseCase is used directly by the searchItemsProvider
  final AddItemUseCase _addItemUseCase;
  final UpdateItemUseCase _updateItemUseCase;
  final DeleteItemUseCase _deleteItemUseCase;
  final IsItemNameExistsUseCase _isItemNameExistsUseCase;

  ItemsNotifier({
    required GetAllItemsUseCase getAllItemsUseCase,
    required SearchItemsUseCase searchItemsUseCase,
    required AddItemUseCase addItemUseCase,
    required UpdateItemUseCase updateItemUseCase,
    required DeleteItemUseCase deleteItemUseCase,
    required IsItemNameExistsUseCase isItemNameExistsUseCase,
  })  : _getAllItemsUseCase = getAllItemsUseCase,
        _addItemUseCase = addItemUseCase,
        _updateItemUseCase = updateItemUseCase,
        _deleteItemUseCase = deleteItemUseCase,
        _isItemNameExistsUseCase = isItemNameExistsUseCase,
        super(const AsyncValue.loading()) {
    refreshItems();
  }

  Future<void> refreshItems() async {
    state = const AsyncValue.loading();
    try {
      final items = await _getAllItemsUseCase();
      state = AsyncValue.data(items);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<bool> isItemNameExists(String name) async {
    try {
      return await _isItemNameExistsUseCase(name);
    } catch (e) {
      return false;
    }
  }

  Future<bool> addItem(String name, double price) async {
    try {
      final item = ItemEntity(
        id: 0, // Will be ignored by the database
        name: name,
        price: price,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _addItemUseCase(item);
      refreshItems();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> updateItem(ItemEntity item, double newPrice) async {
    try {
      final updatedItem = item.copyWith(
        price: newPrice,
        updatedAt: DateTime.now(),
      );

      await _updateItemUseCase(updatedItem);
      refreshItems();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteItem(int id) async {
    try {
      await _deleteItemUseCase(id);
      refreshItems();
      return true;
    } catch (e) {
      return false;
    }
  }
}

// Items provider
final itemsProvider = StateNotifierProvider<ItemsNotifier, AsyncValue<List<ItemEntity>>>((ref) {
  return ItemsNotifier(
    getAllItemsUseCase: ref.watch(getAllItemsUseCaseProvider),
    searchItemsUseCase: ref.watch(searchItemsUseCaseProvider),
    addItemUseCase: ref.watch(addItemUseCaseProvider),
    updateItemUseCase: ref.watch(updateItemUseCaseProvider),
    deleteItemUseCase: ref.watch(deleteItemUseCaseProvider),
    isItemNameExistsUseCase: ref.watch(isItemNameExistsUseCaseProvider),
  );
});

// Search items provider
final searchItemsProvider = FutureProvider.family<List<ItemEntity>, String>((ref, query) async {
  final searchItemsUseCase = ref.watch(searchItemsUseCaseProvider);
  return searchItemsUseCase(query);
});
