import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/payment.dart';
import '../../domain/entities/paid_item.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/entities/transaction_with_items.dart';
import '../../domain/entities/transaction_item_with_details.dart';

import '../../domain/usecases/transaction_usecases.dart';
import 'providers.dart';
import 'transaction_history_provider.dart';

// Helper class to track unpaid items across transactions
class _UnpaidItemInfo {
  final TransactionEntity transaction;
  final TransactionItemWithDetailsEntity itemWithDetails;
  final double totalPrice;

  _UnpaidItemInfo({
    required this.transaction,
    required this.itemWithDetails,
    required this.totalPrice,
  });
}

// Unpaid transactions notifier (restored to transaction-level for now)
class UnpaidTransactionsNotifier extends StateNotifier<AsyncValue<List<TransactionEntity>>> {
  final GetUnpaidTransactionsUseCase _getUnpaidTransactionsUseCase;
  final GetTransactionWithItemsUseCase _getTransactionWithItemsUseCase;
  final AddPaymentAndUpdateTransactionUseCase _addPaymentAndUpdateTransactionUseCase;
  final Ref _ref;

  UnpaidTransactionsNotifier({
    required GetUnpaidTransactionsUseCase getUnpaidTransactionsUseCase,
    required GetTransactionWithItemsUseCase getTransactionWithItemsUseCase,
    required AddPaymentAndUpdateTransactionUseCase addPaymentAndUpdateTransactionUseCase,
    required Ref ref,
  })  : _getUnpaidTransactionsUseCase = getUnpaidTransactionsUseCase,
        _getTransactionWithItemsUseCase = getTransactionWithItemsUseCase,
        _addPaymentAndUpdateTransactionUseCase = addPaymentAndUpdateTransactionUseCase,
        _ref = ref,
        super(const AsyncValue.loading()) {
    refreshTransactions();
  }

  // Calculate the grand total of all unpaid transactions
  double getGrandTotal() {
    if (state is! AsyncData) {
      return 0.0;
    }

    final transactions = (state as AsyncData<List<TransactionEntity>>).value;
    return transactions.fold(0.0, (sum, transaction) => sum + transaction.remainingAmount);
  }

  Future<void> refreshTransactions() async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _getUnpaidTransactionsUseCase();

      // Sort transactions by date (newest first) by default
      transactions.sort((a, b) => b.date.compareTo(a.date));

      state = AsyncValue.data(transactions);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<TransactionWithItemsEntity> getTransactionWithItems(int transactionId) async {
    return await _getTransactionWithItemsUseCase(transactionId);
  }

  Future<bool> addPayment(int transactionId, double amount) async {
    try {
      // Get the current transaction
      final transaction = state.value?.firstWhere(
        (t) => t.id == transactionId,
        orElse: () => throw Exception('Transaction not found'),
      );

      if (transaction == null) {
        return false;
      }

      // Get the transaction with items
      final transactionWithItems = await _getTransactionWithItemsUseCase(transactionId);

      // Calculate the new remaining amount
      final newRemainingAmount = transaction.remainingAmount - amount;

      // Determine the new status
      final newStatus = newRemainingAmount <= 0
          ? AppConstants.statusPaid
          : AppConstants.statusPartiallyPaid;

      // Create the payment entity
      final payment = PaymentEntity(
        id: 0, // Will be ignored by the database
        transactionId: transactionId,
        amount: amount,
        date: DateTime.now(),
        createdAt: DateTime.now(),
      );

      // Create the updated transaction entity
      final updatedTransaction = transaction.copyWith(
        remainingAmount: newRemainingAmount > 0 ? newRemainingAmount : 0,
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      // Create paid items entities
      final paidItems = <PaidItemEntity>[];

      if (newStatus == AppConstants.statusPaid) {
        // If fully paid, add all items
        for (final item in transactionWithItems.items) {
          paidItems.add(
            PaidItemEntity(
              id: 0, // Will be ignored by the database
              paymentId: 0, // Will be set by the database
              transactionItemId: item.transactionItem.id,
              quantity: item.transactionItem.quantity,
              amount: item.transactionItem.quantity * item.transactionItem.priceAtPurchase,
              createdAt: DateTime.now(),
            ),
          );
        }
      } else {
        // If partially paid, calculate how many items can be fully paid
        // For simplicity, we'll just mark all items as partially paid with the same ratio
        final paymentRatio = amount / transaction.remainingAmount;

        for (final item in transactionWithItems.items) {
          final itemAmount = item.transactionItem.quantity * item.transactionItem.priceAtPurchase;
          final paidAmount = itemAmount * paymentRatio;

          paidItems.add(
            PaidItemEntity(
              id: 0, // Will be ignored by the database
              paymentId: 0, // Will be set by the database
              transactionItemId: item.transactionItem.id,
              quantity: (item.transactionItem.quantity * paymentRatio).round(),
              amount: paidAmount,
              createdAt: DateTime.now(),
            ),
          );
        }
      }

      // Add the payment and update the transaction
      await _addPaymentAndUpdateTransactionUseCase(payment, updatedTransaction, paidItems);

      // Refresh the transactions list
      refreshTransactions();

      // Always refresh the transaction history when any payment is made
      // This ensures partial payments also appear in the history
      _ref.read(baseTransactionHistoryProvider.notifier).refreshTransactions();

      return true;
    } catch (e) {
      debugPrint('Error in addPayment: $e');
      return false;
    }
  }

  // Add a global payment that is distributed across individual items
  Future<bool> addGlobalPayment(double amount, DateTime paymentDate) async {
    try {
      if (state is! AsyncData || amount <= 0) {
        return false;
      }

      // Get all unpaid transactions
      final transactions = List<TransactionEntity>.from((state as AsyncData<List<TransactionEntity>>).value);

      // If there are no transactions, return false
      if (transactions.isEmpty) {
        return false;
      }

      // Collect all unpaid items across all transactions
      final allUnpaidItems = <_UnpaidItemInfo>[];

      for (final transaction in transactions) {
        final transactionWithItems = await _getTransactionWithItemsUseCase(transaction.id);

        for (final item in transactionWithItems.items) {
          final itemTotalPrice = item.transactionItem.quantity * item.transactionItem.priceAtPurchase;
          allUnpaidItems.add(_UnpaidItemInfo(
            transaction: transaction,
            itemWithDetails: item,
            totalPrice: itemTotalPrice,
          ));
        }
      }

      // Sort items by price (ascending) to optimize selection
      allUnpaidItems.sort((a, b) => a.totalPrice.compareTo(b.totalPrice));

      // Select items that total the payment amount using a greedy algorithm
      final selectedItems = _selectItemsForPayment(allUnpaidItems, amount);

      if (selectedItems.isEmpty) {
        return false;
      }

      // Group selected items by transaction for processing
      final itemsByTransaction = <int, List<_UnpaidItemInfo>>{};
      for (final item in selectedItems) {
        final transactionId = item.transaction.id;
        itemsByTransaction.putIfAbsent(transactionId, () => []).add(item);
      }

      bool atLeastOnePaymentMade = false;

      // Process each transaction that has selected items
      for (final entry in itemsByTransaction.entries) {
        final transactionId = entry.key;
        final selectedItemsForTransaction = entry.value;

        // Get the original transaction
        final transaction = transactions.firstWhere((t) => t.id == transactionId);

        // Calculate total amount being paid for this transaction
        final totalPaidForTransaction = selectedItemsForTransaction
            .fold(0.0, (sum, item) => sum + item.totalPrice);

        // Create payment entity
        final payment = PaymentEntity(
          id: 0,
          transactionId: transactionId,
          amount: totalPaidForTransaction,
          date: paymentDate,
          createdAt: DateTime.now(),
        );

        // Create paid items entities for selected items only
        final paidItems = <PaidItemEntity>[];
        for (final selectedItem in selectedItemsForTransaction) {
          paidItems.add(
            PaidItemEntity(
              id: 0,
              paymentId: 0, // Will be set by the database
              transactionItemId: selectedItem.itemWithDetails.transactionItem.id,
              quantity: selectedItem.itemWithDetails.transactionItem.quantity,
              amount: selectedItem.totalPrice,
              createdAt: DateTime.now(),
            ),
          );
        }

        // Calculate new remaining amount for the transaction
        final newRemainingAmount = transaction.remainingAmount - totalPaidForTransaction;

        // Determine new status
        final newStatus = newRemainingAmount <= 0
            ? AppConstants.statusPaid
            : AppConstants.statusPartiallyPaid;

        // Create updated transaction
        final updatedTransaction = transaction.copyWith(
          remainingAmount: newRemainingAmount > 0 ? newRemainingAmount : 0,
          status: newStatus,
          updatedAt: DateTime.now(),
        );

        try {
          // Add payment and update transaction
          await _addPaymentAndUpdateTransactionUseCase(payment, updatedTransaction, paidItems);
          atLeastOnePaymentMade = true;
        } catch (e) {
          // Log the error but continue with other transactions
          debugPrint('Error updating transaction $transactionId: $e');
        }
      }

      // Refresh transactions list
      await refreshTransactions();

      // Refresh transaction history if any payments were made
      if (atLeastOnePaymentMade) {
        _ref.read(baseTransactionHistoryProvider.notifier).refreshTransactions();
      }

      // Return true only if at least one payment was successfully made
      return atLeastOnePaymentMade;
    } catch (e) {
      debugPrint('Error in addGlobalPayment: $e');
      return false;
    }
  }

  // Helper method to select items for payment using a greedy algorithm
  List<_UnpaidItemInfo> _selectItemsForPayment(List<_UnpaidItemInfo> allItems, double paymentAmount) {
    final selectedItems = <_UnpaidItemInfo>[];
    double remainingAmount = paymentAmount;

    // Sort items by price (ascending) to try to get exact matches
    final sortedItems = List<_UnpaidItemInfo>.from(allItems);
    sortedItems.sort((a, b) => a.totalPrice.compareTo(b.totalPrice));

    // First pass: Try to find items that exactly match or are close to the remaining amount
    for (final item in sortedItems) {
      if (item.totalPrice <= remainingAmount) {
        selectedItems.add(item);
        remainingAmount -= item.totalPrice;

        // If we've allocated the exact amount, we're done
        if (remainingAmount == 0) {
          break;
        }
      }
    }

    // If we still have remaining amount and no exact match was found,
    // try to find the best combination by removing some items and adding others
    if (remainingAmount > 0 && selectedItems.isNotEmpty) {
      // For simplicity, we'll keep the current selection
      // In a more sophisticated algorithm, we could try different combinations
    }

    return selectedItems;
  }

  void sortTransactions(String sortBy) {
    if (state is! AsyncData) {
      return;
    }

    final transactions = List<TransactionEntity>.from((state as AsyncData<List<TransactionEntity>>).value);

    switch (sortBy) {
      case 'date':
        transactions.sort((a, b) => b.date.compareTo(a.date));
        break;
      case 'amount':
        transactions.sort((a, b) => b.remainingAmount.compareTo(a.remainingAmount));
        break;
      case 'status':
        transactions.sort((a, b) => a.status.compareTo(b.status));
        break;
    }

    state = AsyncValue.data(transactions);
  }
}

// Unpaid transactions provider
final unpaidTransactionsProvider = StateNotifierProvider<UnpaidTransactionsNotifier, AsyncValue<List<TransactionEntity>>>((ref) {
  return UnpaidTransactionsNotifier(
    getUnpaidTransactionsUseCase: ref.watch(getUnpaidTransactionsUseCaseProvider),
    getTransactionWithItemsUseCase: ref.watch(getTransactionWithItemsUseCaseProvider),
    addPaymentAndUpdateTransactionUseCase: ref.watch(addPaymentAndUpdateTransactionUseCaseProvider),
    ref: ref,
  );
});

// Transaction with items provider
final transactionWithItemsProvider = FutureProvider.family<TransactionWithItemsEntity, int>((ref, transactionId) async {
  final unpaidTransactionsNotifier = ref.watch(unpaidTransactionsProvider.notifier);
  return unpaidTransactionsNotifier.getTransactionWithItems(transactionId);
});
