import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../domain/entities/item.dart';
import '../../providers/items_provider.dart';
import '../../widgets/empty_state.dart';
import '../../widgets/error_display.dart';
import '../../widgets/loading_indicator.dart';
import 'widgets/add_edit_item_bottom_sheet.dart';
import 'widgets/item_list_tile.dart';

class ItemsScreen extends ConsumerWidget {
  const ItemsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final itemsState = ref.watch(itemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleItems),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(itemsProvider.notifier).refreshItems(),
        child: itemsState.when(
          data: (items) {
            if (items.isEmpty) {
              return const EmptyState(
                message: AppConstants.emptyItems,
                icon: Icons.inventory_2_outlined,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return ItemListTile(
                  item: item,
                  onEdit: () => _showEditItemBottomSheet(context, ref, item),
                  onDelete: () => _confirmDeleteItem(context, ref, item.id),
                );
              },
            );
          },
          loading: () => const LoadingIndicator(),
          error: (error, stackTrace) => ErrorDisplay(
            message: 'Error loading items: $error',
            onRetry: () => ref.read(itemsProvider.notifier).refreshItems(),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddItemBottomSheet(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddItemBottomSheet(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: const AddEditItemBottomSheet(),
      ),
    );
  }

  void _showEditItemBottomSheet(BuildContext context, WidgetRef ref, ItemEntity item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: AddEditItemBottomSheet(item: item),
      ),
    );
  }

  Future<void> _confirmDeleteItem(BuildContext context, WidgetRef ref, int itemId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Item'),
        content: const Text(AppConstants.confirmDeleteItem),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(AppConstants.buttonCancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text(AppConstants.buttonDelete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      if (context.mounted) {
        await ref.read(itemsProvider.notifier).deleteItem(itemId);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text(AppConstants.successItemDeleted)),
          );
        }
      }
    }
  }
}

class ItemSearchDelegate extends SearchDelegate {
  final WidgetRef ref;

  ItemSearchDelegate(this.ref);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter an item name to search'),
      );
    }

    return Consumer(
      builder: (context, ref, child) {
        final searchResultsAsyncValue = ref.watch(searchItemsProvider(query));

        return searchResultsAsyncValue.when(
          data: (items) {
            if (items.isEmpty) {
              return const EmptyState(
                message: AppConstants.emptySearchResults,
                icon: Icons.search_off,
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              itemCount: items.length,
              itemBuilder: (context, index) {
                final item = items[index];
                return ItemListTile(
                  item: item,
                  onEdit: () {
                    close(context, null);
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
                        ),
                      ),
                      builder: (context) => Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        child: AddEditItemBottomSheet(item: item),
                      ),
                    );
                  },
                  onDelete: () async {
                    final confirmed = await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Delete Item'),
                        content: const Text(AppConstants.confirmDeleteItem),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text(AppConstants.buttonCancel),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            style: TextButton.styleFrom(
                              foregroundColor: Theme.of(context).colorScheme.error,
                            ),
                            child: const Text(AppConstants.buttonDelete),
                          ),
                        ],
                      ),
                    );

                    if (confirmed == true) {
                      if (context.mounted) {
                        await ref.read(itemsProvider.notifier).deleteItem(item.id);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text(AppConstants.successItemDeleted)),
                          );
                          close(context, null);
                        }
                      }
                    }
                  },
                );
              },
            );
          },
          loading: () => const LoadingIndicator(),
          error: (error, stackTrace) => ErrorDisplay(
            message: 'Error searching items: $error',
            onRetry: () => ref.refresh(searchItemsProvider(query)),
          ),
        );
      },
    );
  }
}
