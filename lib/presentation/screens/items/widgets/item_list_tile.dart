import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../domain/entities/item.dart';

class ItemListTile extends StatelessWidget {
  final ItemEntity item;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const ItemListTile({
    super.key,
    required this.item,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: [
            SlidableAction(
              onPressed: (_) => onEdit(),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              icon: Icons.edit,
              label: AppConstants.buttonUpdate,
            ),
            SlidableAction(
              onPressed: (_) => onDelete(),
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
              icon: Icons.delete,
              label: AppConstants.buttonDelete,
            ),
          ],
        ),
        child: Card(
          elevation: 2,
          child: ListTile(
            contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
            title: Text(
              item.name,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: AppConstants.smallPadding / 2),
              child: Text(
                Formatters.formatCurrency(item.price),
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: onEdit,
          ),
        ),
      ),
    );
  }
}
