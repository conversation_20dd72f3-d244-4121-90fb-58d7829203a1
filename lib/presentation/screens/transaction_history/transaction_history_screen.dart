import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/formatters.dart';
import '../../../domain/entities/transaction_with_payments.dart';
import '../../providers/transaction_history_provider.dart' as provider;
import '../../widgets/empty_state.dart';
import '../../widgets/error_display.dart';
import '../../widgets/loading_indicator.dart';

class TransactionHistoryScreen extends ConsumerWidget {
  const TransactionHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleTransactionHistory),
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => ref.read(provider.baseTransactionHistoryProvider.notifier).refreshTransactions(),
              child: Consumer(
                builder: (context, ref, child) {
                  final groupedTransactionsState = ref.watch(provider.paymentDateGroupedTransactionsProvider);

                  return groupedTransactionsState.when(
                    data: (groupedTransactions) {
                      if (groupedTransactions.isEmpty) {
                        return const EmptyState(
                          message: AppConstants.emptyTransactionHistory,
                          icon: Icons.history_outlined,
                        );
                      }

                      // Sort dates in descending order (most recent first)
                      final sortedDates = groupedTransactions.keys.toList()
                        ..sort((a, b) => b.compareTo(a));

                      return ListView.builder(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        itemCount: sortedDates.length,
                        itemBuilder: (context, index) {
                          final date = sortedDates[index];
                          final dateTransactions = groupedTransactions[date]!;

                          // Calculate total payment amount for this date
                          double totalPaymentAmount = 0;
                          for (final transaction in dateTransactions) {
                            for (final payment in transaction.payments) {
                              // Only count payments made on this date
                              if (DateTime(payment.date.year, payment.date.month, payment.date.day).isAtSameMomentAs(date)) {
                                totalPaymentAmount += payment.amount;
                              }
                            }
                          }

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      DateFormat(AppConstants.dateFormat).format(date),
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Total payment: ${Formatters.formatCurrency(totalPaymentAmount)}',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            color: Theme.of(context).colorScheme.primary,
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                              Card(
                                margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                                child: Padding(
                                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Items paid on this date:',
                                        style: Theme.of(context).textTheme.titleSmall,
                                      ),
                                      const SizedBox(height: AppConstants.smallPadding),
                                      _buildCombinedItemsList(context, ref, dateTransactions, date),
                                    ],
                                  ),
                                ),
                              ),
                              if (index < sortedDates.length - 1)
                                const Divider(height: AppConstants.defaultPadding * 2),
                            ],
                          );
                        },
                      );
                    },
                    loading: () => const LoadingIndicator(),
                    error: (error, stackTrace) => ErrorDisplay(
                      message: 'Error loading transaction history: $error',
                      onRetry: () => ref.read(provider.baseTransactionHistoryProvider.notifier).refreshTransactions(),
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      );
  }


  Widget _buildCombinedItemsList(
    BuildContext context,
    WidgetRef ref,
    List<TransactionWithPaymentsEntity> transactions,
    DateTime paymentDate
  ) {
    return Consumer(
      builder: (context, ref, child) {
        final paidItemsAsyncValue = ref.watch(provider.paidItemsByDateProvider(paymentDate));

        return paidItemsAsyncValue.when(
          data: (groupedItems) {
            if (groupedItems.isEmpty) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: Text('No items found for this payment date'),
              );
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: groupedItems.map((groupedItem) {
                return ListTile(
                  dense: true,
                  title: Text(
                    groupedItem.itemName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Text(
                    '${groupedItem.totalQuantity} x ${Formatters.formatCurrency(groupedItem.totalAmount / groupedItem.totalQuantity)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  trailing: Text(
                    Formatters.formatCurrency(groupedItem.totalAmount),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }).toList(),
            );
          },
          loading: () => const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
          ),
          error: (error, stackTrace) => Text(
            'Error loading items: $error',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        );
      },
    );
  }
}
