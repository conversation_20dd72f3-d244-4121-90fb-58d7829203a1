import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../domain/entities/payment.dart';

class PaymentHistoryList extends StatelessWidget {
  final List<PaymentEntity> payments;

  const PaymentHistoryList({
    super.key,
    required this.payments,
  });

  @override
  Widget build(BuildContext context) {
    if (payments.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding),
        child: Text(
          'No payment records found',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
        ),
      );
    }

    // Sort payments by date (newest first)
    final sortedPayments = List<PaymentEntity>.from(payments)
      ..sort((a, b) => b.date.compareTo(a.date));

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: sortedPayments.length,
      itemBuilder: (context, index) {
        final payment = sortedPayments[index];
        
        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  Formatters.formatDateTime(payment.date),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  Formatters.formatCurrency(payment.amount),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
