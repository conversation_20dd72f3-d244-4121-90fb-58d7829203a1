import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../domain/entities/transaction_with_payments.dart';
import '../../../providers/unpaid_transactions_provider.dart';
import '../../../widgets/loading_indicator.dart';
import '../../unpaid_transactions/widgets/transaction_items_list.dart';
import 'payment_history_list.dart';

class TransactionHistoryCard extends ConsumerStatefulWidget {
  final TransactionWithPaymentsEntity transaction;

  const TransactionHistoryCard({
    super.key,
    required this.transaction,
  });

  @override
  ConsumerState<TransactionHistoryCard> createState() => _TransactionHistoryCardState();
}

class _TransactionHistoryCardState extends ConsumerState<TransactionHistoryCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final transaction = widget.transaction.transaction;
    final payments = widget.transaction.payments;

    // Calculate total paid amount
    final totalPaid = payments.fold(0.0, (sum, payment) => sum + payment.amount);

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Column(
        children: [
          ListTile(
            contentPadding: const EdgeInsets.all(AppConstants.defaultPadding),
            title: Text(
              'Transaction on ${Formatters.formatDate(transaction.date)}',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: AppConstants.smallPadding),
                Row(
                  children: [
                    Text(
                      '${AppConstants.labelTotalAmount}: ',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      Formatters.formatCurrency(transaction.totalAmount),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Paid: ',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      Formatters.formatCurrency(totalPaid),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                if (payments.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Last payment: ${Formatters.formatDate(payments.first.date)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ],
            ),
            trailing: IconButton(
              icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
              onPressed: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
            ),
          ),
          if (_isExpanded)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  Text(
                    'Items',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Consumer(
                    builder: (context, ref, child) {
                      final transactionWithItemsAsyncValue = ref.watch(
                        transactionWithItemsProvider(transaction.id),
                      );

                      return transactionWithItemsAsyncValue.when(
                        data: (transactionWithItems) {
                          return TransactionItemsList(
                            items: transactionWithItems.items,
                          );
                        },
                        loading: () => const SizedBox(
                          height: 100,
                          child: Center(
                            child: LoadingIndicator(),
                          ),
                        ),
                        error: (error, stackTrace) => Text(
                          'Error loading items: $error',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.error,
                              ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Text(
                    'Payment History',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  PaymentHistoryList(payments: payments),
                  const SizedBox(height: AppConstants.defaultPadding),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
