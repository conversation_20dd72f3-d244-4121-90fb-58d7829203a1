import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../domain/entities/transaction_item_with_details.dart';

class TransactionItemsList extends StatelessWidget {
  final List<TransactionItemWithDetailsEntity> items;

  const TransactionItemsList({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final totalPrice = item.transactionItem.quantity * item.transactionItem.priceAtPurchase;
        final remainingAmount = item.transactionItem.remainingAmount;
        final isPartiallyPaid = remainingAmount < totalPrice;

        return Padding(
          padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.item.name,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    if (isPartiallyPaid)
                      Text(
                        'Partially paid',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${item.transactionItem.quantity} x ${Formatters.formatCurrency(item.transactionItem.priceAtPurchase)}',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      Formatters.formatCurrency(remainingAmount),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isPartiallyPaid ? Colors.orange : null,
                          ),
                      textAlign: TextAlign.end,
                    ),
                    if (isPartiallyPaid)
                      Text(
                        'of ${Formatters.formatCurrency(totalPrice)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                          decoration: TextDecoration.lineThrough,
                        ),
                        textAlign: TextAlign.end,
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
