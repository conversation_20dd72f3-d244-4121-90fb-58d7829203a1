import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/utils/validators.dart';
import '../../../providers/unpaid_transactions_provider.dart';
import '../../../widgets/currency_text_field.dart';

class GlobalPaymentBottomSheet extends ConsumerStatefulWidget {
  final double grandTotal;

  const GlobalPaymentBottomSheet({
    super.key,
    required this.grandTotal,
  });

  @override
  ConsumerState<GlobalPaymentBottomSheet> createState() => _GlobalPaymentBottomSheetState();
}

class _GlobalPaymentBottomSheetState extends ConsumerState<GlobalPaymentBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _amountController;
  bool _isLoading = false;
  late final DateTime _paymentDate;

  @override
  void initState() {
    super.initState();
    // Format the amount properly to avoid parsing issues
    _amountController = TextEditingController(text: widget.grandTotal.toStringAsFixed(0));
    _paymentDate = DateTime.now();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _makeGlobalPayment() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final amount = Formatters.parseCurrency(_amountController.text);

    final success = await ref.read(unpaidTransactionsProvider.notifier).addGlobalPayment(
      amount,
      _paymentDate,
    );

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppConstants.successPaymentAdded)),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to add payment')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: AppConstants.defaultPadding,
        right: AppConstants.defaultPadding,
        top: AppConstants.defaultPadding,
        bottom: MediaQuery.of(context).viewInsets.bottom + AppConstants.defaultPadding,
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                AppConstants.titleGlobalPayment,
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                '${AppConstants.labelGrandTotal}: ${Formatters.formatCurrency(widget.grandTotal)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                '${AppConstants.labelDate}: ${Formatters.formatDate(_paymentDate)}',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CurrencyTextField(
                controller: _amountController,
                label: AppConstants.labelPaymentAmount,
                validator: (value) => Validators.validatePaymentAmount(value, widget.grandTotal),
              ),
              const SizedBox(height: AppConstants.largePadding),
              ElevatedButton(
                onPressed: _isLoading ? null : _makeGlobalPayment,
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(AppConstants.buttonPay),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(AppConstants.buttonCancel),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
