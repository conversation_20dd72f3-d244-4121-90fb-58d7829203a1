import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../../../../core/utils/validators.dart';
import '../../../../domain/entities/item.dart';
import '../../../providers/items_provider.dart';
import '../../../providers/new_transaction_provider.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/empty_state.dart';
import '../../../widgets/error_display.dart';
import '../../../widgets/loading_indicator.dart';

class AddTransactionItemBottomSheet extends ConsumerStatefulWidget {
  const AddTransactionItemBottomSheet({super.key});

  @override
  ConsumerState<AddTransactionItemBottomSheet> createState() => _AddTransactionItemBottomSheetState();
}

class _AddTransactionItemBottomSheetState extends ConsumerState<AddTransactionItemBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _quantityController = TextEditingController(text: '1');
  int? _selectedItemId;
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  void _addItemToTransaction() {
    if (_formKey.currentState?.validate() != true || _selectedItemId == null) {
      return;
    }

    final quantity = int.tryParse(_quantityController.text) ?? 1;
    if (quantity < 1) {
      return;
    }

    final itemsState = ref.read(itemsProvider);
    if (itemsState is AsyncData && itemsState.value != null) {
      try {
        final selectedItem = itemsState.value!.firstWhere(
          (item) => item.id == _selectedItemId,
          orElse: () => throw Exception('Item not found'),
        );

        ref.read(newTransactionProvider.notifier).addItem(selectedItem, quantity);

        if (mounted) {
          Navigator.of(context).maybePop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: AppConstants.defaultPadding,
        right: AppConstants.defaultPadding,
        top: AppConstants.defaultPadding,
        // Add bottom padding to account for keyboard
        bottom: MediaQuery.of(context).viewInsets.bottom + AppConstants.defaultPadding,
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Add Item to Transaction',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                controller: _searchController,
                label: AppConstants.labelSearch,
                hint: 'Search for an item',
                prefixIcon: const Icon(Icons.search),
                onChanged: (value) {
                  setState(() {
                    _isSearching = value.isNotEmpty;
                    _selectedItemId = null;
                  });
                },
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              SizedBox(
                height: 200,
                child: _buildItemsList(),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              CustomTextField(
                controller: _quantityController,
                label: AppConstants.labelQuantity,
                validator: Validators.validateQuantity,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
              const SizedBox(height: AppConstants.largePadding),
              ElevatedButton(
                onPressed: _selectedItemId == null ? null : _addItemToTransaction,
                child: const Text('Add to Transaction'),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              TextButton(
                onPressed: () {
                  if (mounted) {
                    Navigator.of(context).maybePop();
                  }
                },
                child: const Text(AppConstants.buttonCancel),
              ),
              // Add extra padding at the bottom to ensure content is visible
              const SizedBox(height: AppConstants.defaultPadding),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    if (_isSearching) {
      return Consumer(
        builder: (context, ref, child) {
          final searchResultsAsyncValue = ref.watch(searchItemsProvider(_searchController.text));

          return searchResultsAsyncValue.when(
            data: (items) {
              if (items.isEmpty) {
                return const EmptyState(
                  message: AppConstants.emptySearchResults,
                  icon: Icons.search_off,
                );
              }

              return ListView.builder(
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return _buildItemTile(item);
                },
              );
            },
            loading: () => const LoadingIndicator(),
            error: (error, stackTrace) => ErrorDisplay(
              message: 'Error searching items: $error',
              onRetry: () => ref.refresh(searchItemsProvider(_searchController.text)),
            ),
          );
        },
      );
    } else {
      return Consumer(
        builder: (context, ref, child) {
          final itemsAsyncValue = ref.watch(itemsProvider);

          return itemsAsyncValue.when(
            data: (items) {
              if (items.isEmpty) {
                return const EmptyState(
                  message: AppConstants.emptyItems,
                  icon: Icons.inventory_2_outlined,
                );
              }

              return ListView.builder(
                itemCount: items.length,
                itemBuilder: (context, index) {
                  final item = items[index];
                  return _buildItemTile(item);
                },
              );
            },
            loading: () => const LoadingIndicator(),
            error: (error, stackTrace) => ErrorDisplay(
              message: 'Error loading items: $error',
              onRetry: () => ref.refresh(itemsProvider),
            ),
          );
        },
      );
    }
  }

  Widget _buildItemTile(ItemEntity item) {
    final isSelected = _selectedItemId == item.id;

    return Card(
      color: isSelected ? Theme.of(context).colorScheme.primary.withAlpha(26) : null,
      child: ListTile(
        title: Text(item.name),
        subtitle: Text(Formatters.formatCurrency(item.price)),
        trailing: isSelected
            ? Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
              )
            : null,
        onTap: () {
          setState(() {
            _selectedItemId = item.id;
          });
        },
      ),
    );
  }
}
