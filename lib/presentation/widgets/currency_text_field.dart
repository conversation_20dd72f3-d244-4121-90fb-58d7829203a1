import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_constants.dart';

class CurrencyTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final String? Function(String?)? validator;
  final bool readOnly;
  final void Function(String)? onChanged;
  final FocusNode? focusNode;

  const CurrencyTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.validator,
    this.readOnly = false,
    this.onChanged,
    this.focusNode,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.labelLarge,
        ),
        const SizedBox(height: AppConstants.smallPadding / 2),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            prefixText: '${AppConstants.currencySymbol} ',
            prefixStyle: Theme.of(context).textTheme.bodyLarge,
          ),
          validator: validator,
          keyboardType: TextInputType.number,
          readOnly: readOnly,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            TextInputFormatter.withFunction((oldValue, newValue) {
              try {
                final text = newValue.text;
                if (text.isEmpty) {
                  return newValue;
                }

                // Parse as integer (no decimal places)
                final number = int.parse(text);
                final formatted = number.toString();

                // Format with thousands separator
                if (formatted != text) {
                  return TextEditingValue(
                    text: formatted,
                    selection: TextSelection.collapsed(offset: formatted.length),
                  );
                }

                return newValue;
              } catch (e) {
                return oldValue;
              }
            }),
          ],
          onChanged: onChanged,
          focusNode: focusNode,
        ),
      ],
    );
  }
}
