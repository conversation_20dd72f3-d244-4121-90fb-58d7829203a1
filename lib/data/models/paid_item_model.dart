import 'package:drift/drift.dart';
import '../../domain/entities/paid_item.dart';
import '../datasources/local/database/database.dart';

extension PaidItemEntityMapper on PaidItemEntity {
  PaidItemsCompanion toCompanion() {
    return PaidItemsCompanion(
      id: Value(id),
      paymentId: Value(paymentId),
      transactionItemId: Value(transactionItemId),
      quantity: Value(quantity),
      amount: Value(amount),
      createdAt: Value(createdAt),
    );
  }
}

extension PaidItemMapper on PaidItem {
  PaidItemEntity toEntity() {
    return PaidItemEntity(
      id: id,
      paymentId: paymentId,
      transactionItemId: transactionItemId,
      quantity: quantity,
      amount: amount,
      createdAt: createdAt,
    );
  }
}
