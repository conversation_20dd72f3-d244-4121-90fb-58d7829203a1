import '../../domain/entities/complete_transaction_details.dart';
import '../datasources/local/database/database.dart';
import 'transaction_model.dart';
import 'transaction_item_with_details_model.dart';
import 'payment_model.dart';

extension CompleteTransactionDetailsMapper on CompleteTransactionDetails {
  CompleteTransactionDetailsEntity toEntity() {
    return CompleteTransactionDetailsEntity(
      transaction: transaction.toEntity(),
      items: items.map((item) => item.toEntity()).toList(),
      payments: payments.map((payment) => payment.toEntity()).toList(),
    );
  }
}
