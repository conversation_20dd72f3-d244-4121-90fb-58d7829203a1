import 'package:drift/drift.dart';
import '../../domain/entities/payment.dart';
import '../datasources/local/database/database.dart';

extension PaymentEntityMapper on PaymentEntity {
  PaymentsCompanion toCompanion() {
    return PaymentsCompanion(
      id: Value(id),
      transactionId: Value(transactionId),
      amount: Value(amount),
      date: Value(date),
      createdAt: Value(createdAt),
    );
  }
}

extension PaymentMapper on Payment {
  PaymentEntity toEntity() {
    return PaymentEntity(
      id: id,
      transactionId: transactionId,
      amount: amount,
      date: date,
      createdAt: createdAt,
    );
  }
}
