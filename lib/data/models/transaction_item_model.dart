import 'package:drift/drift.dart';
import '../../domain/entities/transaction_item.dart';
import '../datasources/local/database/database.dart';

extension TransactionItemEntityMapper on TransactionItemEntity {
  TransactionItemsCompanion toCompanion() {
    return TransactionItemsCompanion(
      id: Value(id),
      transactionId: Value(transactionId),
      itemId: Value(itemId),
      quantity: Value(quantity),
      priceAtPurchase: Value(priceAtPurchase),
      remainingAmount: Value(remainingAmount),
      createdAt: Value(createdAt),
    );
  }
}

extension TransactionItemMapper on TransactionItem {
  TransactionItemEntity toEntity() {
    return TransactionItemEntity(
      id: id,
      transactionId: transactionId,
      itemId: itemId,
      quantity: quantity,
      priceAtPurchase: priceAtPurchase,
      remainingAmount: remainingAmount,
      createdAt: createdAt,
    );
  }
}
