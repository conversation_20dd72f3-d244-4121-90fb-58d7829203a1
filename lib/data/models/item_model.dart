import 'package:drift/drift.dart';
import '../../domain/entities/item.dart';
import '../datasources/local/database/database.dart';

extension ItemEntityMapper on ItemEntity {
  ItemsCompanion toCompanion() {
    return ItemsCompanion(
      id: Value(id),
      name: Value(name),
      price: Value(price),
      createdAt: Value(createdAt),
      updatedAt: Value(DateTime.now()),
    );
  }
}

extension ItemMapper on Item {
  ItemEntity toEntity() {
    return ItemEntity(
      id: id,
      name: name,
      price: price,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
