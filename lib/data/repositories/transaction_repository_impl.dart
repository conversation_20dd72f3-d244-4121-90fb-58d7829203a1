import 'package:drift/drift.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/entities/transaction_with_items.dart';
import '../../domain/entities/transaction_with_payments.dart';
import '../../domain/entities/complete_transaction_details.dart';
import '../../domain/entities/transaction_item.dart';
import '../../domain/entities/transaction_item_with_details.dart';
import '../../domain/entities/payment.dart';
import '../../domain/entities/paid_item.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../datasources/local/database/database.dart';
import '../models/transaction_model.dart';
import '../models/transaction_with_items_model.dart';
import '../models/transaction_with_payments_model.dart';
import '../models/complete_transaction_details_model.dart';
import '../models/transaction_item_with_details_model.dart';
import '../models/paid_item_model.dart';

class TransactionRepositoryImpl implements TransactionRepository {
  final AppDatabase database;

  TransactionRepositoryImpl(this.database);

  @override
  Future<List<TransactionEntity>> getAllTransactions() async {
    final transactions = await database.getAllTransactions();
    return transactions.map((transaction) => transaction.toEntity()).toList();
  }

  @override
  Future<List<TransactionEntity>> getUnpaidTransactions() async {
    final transactions = await database.getUnpaidTransactions();
    return transactions.map((transaction) => transaction.toEntity()).toList();
  }

  @override
  Future<List<TransactionEntity>> getPaidTransactions() async {
    final transactions = await database.getPaidTransactions();
    return transactions.map((transaction) => transaction.toEntity()).toList();
  }

  @override
  Future<TransactionEntity> getTransactionById(int id) async {
    final transaction = await database.getTransactionById(id);
    return transaction.toEntity();
  }

  @override
  Future<TransactionWithItemsEntity> getTransactionWithItems(int transactionId) async {
    final transactionWithItems = await database.getTransactionWithItems(transactionId);
    return transactionWithItems.toEntity();
  }

  @override
  Future<TransactionWithPaymentsEntity> getTransactionWithPayments(int transactionId) async {
    final transactionWithPayments = await database.getTransactionWithPayments(transactionId);
    return transactionWithPayments.toEntity();
  }

  @override
  Future<CompleteTransactionDetailsEntity> getCompleteTransactionDetails(int transactionId) async {
    final completeDetails = await database.getCompleteTransactionDetails(transactionId);
    return completeDetails.toEntity();
  }

  @override
  Future<int> createTransactionWithItems(
    TransactionEntity transaction,
    List<TransactionItemEntity> items,
  ) {
    final transactionCompanion = TransactionsCompanion(
      date: Value(transaction.date),
      totalAmount: Value(transaction.totalAmount),
      remainingAmount: Value(transaction.remainingAmount),
      status: Value(transaction.status),
      createdAt: Value(DateTime.now()),
      updatedAt: Value(DateTime.now()),
    );

    final transactionItemsCompanions = items.map((item) {
      return TransactionItemsCompanion(
        itemId: Value(item.itemId),
        quantity: Value(item.quantity),
        priceAtPurchase: Value(item.priceAtPurchase),
        remainingAmount: Value(item.remainingAmount),
        createdAt: Value(DateTime.now()),
      );
    }).toList();

    return database.createTransactionWithItems(
      transactionCompanion,
      transactionItemsCompanions,
    );
  }

  @override
  Future<int> addPaymentAndUpdateTransaction(
    PaymentEntity payment,
    TransactionEntity updatedTransaction,
    List<PaidItemEntity> paidItems,
  ) async {
    final paymentCompanion = PaymentsCompanion(
      transactionId: Value(payment.transactionId),
      amount: Value(payment.amount),
      date: Value(payment.date),
      createdAt: Value(DateTime.now()),
    );

    // Convert paid items to companions
    final paidItemsCompanions = paidItems.map((paidItem) => PaidItemsCompanion(
      paymentId: const Value.absent(), // Will be set in the database method
      transactionItemId: Value(paidItem.transactionItemId),
      quantity: Value(paidItem.quantity),
      amount: Value(paidItem.amount),
      createdAt: Value(DateTime.now()),
    )).toList();

    // Use the new method that handles everything in a single transaction
    final paymentId = await database.addPaymentWithPaidItemsAndUpdateTransaction(
      paymentCompanion,
      updatedTransaction.toCompanion(),
      paidItemsCompanions,
    );

    return paymentId;
  }

  @override
  Future<List<PaidItemEntity>> getPaymentPaidItems(int paymentId) async {
    final paidItems = await database.getPaymentPaidItems(paymentId);
    return paidItems.map((paidItem) => paidItem.toEntity()).toList();
  }

  @override
  Future<List<PaidItemEntity>> getPaidItemsForTransactionItem(int transactionItemId) async {
    final paidItems = await database.getPaidItemsForTransactionItem(transactionItemId);
    return paidItems.map((paidItem) => paidItem.toEntity()).toList();
  }

  @override
  Future<List<TransactionItemWithDetailsEntity>> getUnpaidTransactionItems() async {
    final unpaidItems = await database.getUnpaidTransactionItems();
    return unpaidItems.map((item) => item.toEntity()).toList();
  }

  @override
  Future<void> updateTransactionItemRemainingAmount(int transactionItemId, double newRemainingAmount) {
    return database.updateTransactionItemRemainingAmount(transactionItemId, newRemainingAmount);
  }

  @override
  Future<void> recalculateTransactionRemainingAmount(int transactionId) {
    return database.recalculateTransactionRemainingAmount(transactionId);
  }
}
