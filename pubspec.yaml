name: beta
description: "Flutter application for managing unpaid purchase transactions"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # Database
  drift: ^2.26.1
  sqlite3_flutter_libs: ^0.5.20
  path_provider: ^2.1.2
  path: ^1.9.0

  # State Management
  flutter_riverpod: ^2.6.1
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.20.5

  # Navigation
  go_router: ^15.1.2

  # UI
  flutter_slidable: ^3.0.1
  intl: ^0.19.0

  # Utils
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  uuid: ^4.3.3
  collection: ^1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.8
  drift_dev: ^2.26.1
  freezed: ^2.4.7
  json_serializable: ^6.7.1

  # Testing
  mocktail: ^1.0.3

flutter:
  uses-material-design: true

  assets:
    - assets/images/
