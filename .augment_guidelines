Development Guidelines

These guidelines are intended to standardize and ensure the quality, scalability, and maintainability of AI Agent applications, following Clean Architecture principles.

- **Always use English**  
  Use English for all code (including variable, class, and file names), comments, documentation, and UI text.

- **Folder Structure**
  - **Feature-based Structure**: Organize code by features, not by component types.
  - **Domain Layer**: Contains business rules and logic, independent of any framework.
  - **Data Layer**: Contains repository implementations and data sources.
  - **Presentation Layer**: Contains UI and state management logic.

- **Dependency Rule**
  - **Direction of Dependency**: Must always point inward: `Domain ← Data ← Presentation`.
  - **Domain Layer**: Must not depend on any other layer.
  - **Data Layer**: May only depend on the Domain Layer.
  - **Presentation Layer**: May depend on both Domain and Data Layers.

- **Entity and Model**
  - **Domain Entity**: Pure data representations without framework dependencies.
  - **Presentation Model**: Transforms domain entities for UI use.

- **Repository Pattern**
  - **Repository Interface**: Defined in the Domain Layer.
  - **Repository Implementation**: Implemented in the Data Layer.

- **Use Case Pattern**
  - Each use case must follow the **Single Responsibility Principle**, handling one specific operation.

- **Error Handling**
  - Use a **consistent error model** across layers.
  - Apply a **Result pattern** (e.g., `Result<Success, Failure>`) to wrap operation results.
  - Handle errors at the controller level to ensure user feedback.

- **Dependency Injection**
  - Use the **Provider Pattern** (preferably with Riverpod) for dependency injection.

- **UI Components**
  - **Separate Widgets**: Break down UI into reusable widget components.
  - **Presentation Logic Separation**: Keep business logic out of UI components.

- **State Management**
  - Use `AsyncValue` (from Riverpod) for managing loading, error, and data states.
  - Use `FutureBuilder` or `AsyncValue.when()` for asynchronous operations.

- **Context Safety**
  - Avoid accessing `BuildContext` after asynchronous gaps to prevent runtime errors.

- **Performance Considerations**
  - Use **lazy loading** for non-critical data.
  - Implement **caching** for frequently accessed data.
  - Use `ref.watch(...select(...))` in Riverpod to avoid unnecessary rebuilds.

- **Consistency**
  - **Naming Conventions**:
  - **File Naming**:

